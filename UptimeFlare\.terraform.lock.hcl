# This file is maintained automatically by "terraform init".
# Manual edits may be lost in future updates.

provider "registry.terraform.io/cloudflare/cloudflare" {
  version     = "4.19.0"
  constraints = "~> 4.0"
  hashes = [
    "h1:tgDfKsBL4SxCgq3sz28oLZ2q+Elzlt1Anpkm7kVC3q8=",
    "zh:1d5315dcbd8187a3a978dc1fb08e80b6cdd353de10afe531b3d1ecb834d0dbae",
    "zh:2a6e5b2e5072e442b35ce6142172e15afb835e16799d04a0054a79d3067f7560",
    "zh:308c5690024a1f6797300018456a1ac781c8699fa4bc4892a8c36eb992604a26",
    "zh:4286969a594396ef09ff6f6840428eef9c7dac037a3a3ef1ccae12a7a21b6655",
    "zh:55cfe536e4fd76ca9a256b905ffa2823b21b5ab6288245c5295a16b03ac4d0b8",
    "zh:58c74a26eef114d59d371f978131d78daa88260df2f75a2b6ec908f61dad2754",
    "zh:5c8dd7ff7820fd96f64e37f5611ad8e265a9b54a04d25e03fe589470ad5d2a0f",
    "zh:6501f10ee5e73ebe3cfe87d4141942c9c784c4ddcde15c6f500b8a41ca3cb174",
    "zh:890df766e9b839623b1f0437355032a3c006226a6c200cd911e15ee1a9014e9f",
    "zh:97f83ab5ff249bd195cfae3b2521f037dea2ad600d70e2917b35895db67c034f",
    "zh:b340b815cc53f197b42d9e64acb6729914706d93dcf60da02e3ac53aeadfde14",
    "zh:c49c8f4908b5776f52211e41880a98a18ebf558363b69ed6af461f2c0d5c9e00",
    "zh:d0ec9cec6a169b160825b0c585b56d42175871549bb291b9409b36086e9e6756",
    "zh:f6912037890a4777a2f8b55858f38fb3428ac53610fb7297d99ced1e73531d5c",
    "zh:f95ca4b80b66b02f1762d314172523ad57176369b7b1aaf4e5018a32f4525582",
  ]
}
