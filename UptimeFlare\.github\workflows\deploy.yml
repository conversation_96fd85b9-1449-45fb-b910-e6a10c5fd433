name: Deploy to <PERSON>flare

on:
  push:
    branches: ['main']
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2.0.3
        with:
          terraform_version: 1.6.4

      - name: Use Node.js 22.x
        uses: actions/setup-node@v3
        with:
          node-version: 22.x
          cache: 'npm'

      # Automatically get an account id via the API Token
      # if secrets.CLOUDFLARE_ACCOUNT_ID is not set.
      - name: Fetch Account ID
        id: fetch_account_id
        run: |
          if [[ -n "${{ secrets.CLOUDFLARE_ACCOUNT_ID }}" ]]; then
            ACCOUNT_ID="${{ secrets.CLOUDFLARE_ACCOUNT_ID }}"
            echo "Using provided CLOUDFLARE_ACCOUNT_ID from secrets."
          else
            ACCOUNT_ID=$(curl -X GET "https://api.cloudflare.com/client/v4/accounts" -H "Authorization: Bearer ${CLOUDFLARE_API_TOKEN}" -H "Content-Type:application/json" | jq ".result[0].id" -r)
            if [[ "$ACCOUNT_ID" == "null" ]]; then
              echo "Failed to get an account id, please make sure you have set up CLOUDFLARE_API_TOKEN correctly!"
              exit 1
            fi
          fi
          echo 'account_id='$ACCOUNT_ID >> $GITHUB_OUTPUT

        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}

      # This is a temporary workaround to fix issue #13
      # On a new Cloudflare account, the terraform apply will fail with `workers.api.error.subdomain_required`
      # This may be due to the account not having a worker subdomain yet, so we create a dummy worker and then delete it.
      # Cloudflare should allocate a worker subdomain after this.
      # https://github.com/cloudflare/terraform-provider-cloudflare/issues/3304
      - name: Create worker subdomain (temporary workaround)
        id: create_dummy_worker
        run: |
          curl --request PUT --fail-with-body \
               --url https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/workers/scripts/dummy-ib4db6ntj5csdef3 \
               --header 'Authorization: Bearer '$CLOUDFLARE_API_TOKEN \
               --header 'Content-Type: application/javascript' \
               --data 'addEventListener('\''fetch'\'', (event) => event.respondWith(new Response('\''OK'\'')))'\

          curl --request DELETE --fail-with-body \
               --url https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/workers/scripts/dummy-ib4db6ntj5csdef3 \
               --header 'Authorization: Bearer '$CLOUDFLARE_API_TOKEN \
               --header 'Content-Type: application/json'
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ steps.fetch_account_id.outputs.account_id }}

      - name: Install packages
        run: |
          npm install
          cd worker
          npm install

      - name: Build worker
        run: |
          cd worker
          npx wrangler deploy src/index.ts --outdir dist --dry-run

      - name: Build page
        run: |
          npx @cloudflare/next-on-pages

      - name: Remove durable objects bindings (temporary workaround)
        continue-on-error: true
        # This is a workaround to fix Cloudflare provider 4.x import crash when there's a durable object binding.
        run: |
          # Get bindings without durable objects
          NEW_BINDINGS=$(curl --request GET --fail-with-body \
               --url https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/workers/scripts/uptimeflare_worker/settings \
               --header 'Authorization: Bearer '$CLOUDFLARE_API_TOKEN \
               --header 'Content-Type: application/json' | jq '.result.bindings | map(select(.type != "durable_object_namespace"))' -jc)
          echo "New bindings: $NEW_BINDINGS"

          # Remove durable objects bindings
          curl --request PATCH --fail-with-body \
               --url https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/workers/scripts/uptimeflare_worker/settings \
               --header 'Authorization: Bearer '$CLOUDFLARE_API_TOKEN \
               -F 'settings={"bindings":'$NEW_BINDINGS'}'
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ steps.fetch_account_id.outputs.account_id }}

      - name: Deploy using Terraform
        # As we don't save terraform state somewhere, we need to import the existing resources
        run: |
          terraform init

          KV_ID=$(curl https://api.cloudflare.com/client/v4/accounts/$TF_VAR_CLOUDFLARE_ACCOUNT_ID/storage/kv/namespaces\?per_page\=100 --header 'Authorization: Bearer '$CLOUDFLARE_API_TOKEN | jq -r '.result[] | select(.title == "uptimeflare_kv") | .id')
          if [ -n "$KV_ID" ]; then
            echo "Importing existing resources..."
            terraform import cloudflare_workers_kv_namespace.uptimeflare_kv "$TF_VAR_CLOUDFLARE_ACCOUNT_ID/$KV_ID"
            terraform import cloudflare_worker_script.uptimeflare "$TF_VAR_CLOUDFLARE_ACCOUNT_ID/uptimeflare_worker"
            terraform import cloudflare_worker_cron_trigger.uptimeflare_worker_cron "$TF_VAR_CLOUDFLARE_ACCOUNT_ID/uptimeflare_worker"
            terraform import cloudflare_pages_project.uptimeflare "$TF_VAR_CLOUDFLARE_ACCOUNT_ID/uptimeflare"
          else
            echo "KV namespace not found, first-time setup."
          fi

          terraform apply -auto-approve -input=false
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          TF_VAR_CLOUDFLARE_ACCOUNT_ID: ${{ steps.fetch_account_id.outputs.account_id }}

      # Terraform Cloudflare provider 4.x doesn't support durable objects, provider 5.x has unresolved issues blocking the deployment. (cloudflare/terraform-provider-cloudflare#5412)
      # So I have to manually add durable objects bindings here.
      - name: Add durable objects bindings
        run: |
          # Get current bindings
          CURRENT_BINDINGS=$(curl --request GET --fail-with-body \
               --url https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/workers/scripts/uptimeflare_worker/settings \
               --header 'Authorization: Bearer '$CLOUDFLARE_API_TOKEN \
               --header 'Content-Type: application/json' | jq '.result.bindings' -jc)
          CURRENT_BINDINGS="${CURRENT_BINDINGS:1:-1}"
          echo "Current bindings: $CURRENT_BINDINGS"

          # Try migration first (required for the new durable object class, ignore failures)
          curl --request PATCH \
               --url https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/workers/scripts/uptimeflare_worker/settings \
               --header 'Authorization: Bearer '$CLOUDFLARE_API_TOKEN \
               -F 'settings={"bindings":[{"type":"durable_object_namespace","name":"REMOTE_CHECKER_DO","class_name":"RemoteChecker"},'$CURRENT_BINDINGS'],"migrations":{"new_sqlite_classes":["RemoteChecker"],"new_tag":"v1"}}'

          # Actually add the binding
          curl --request PATCH --fail-with-body \
               --url https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/workers/scripts/uptimeflare_worker/settings \
               --header 'Authorization: Bearer '$CLOUDFLARE_API_TOKEN \
               -F 'settings={"bindings":[{"type":"durable_object_namespace","name":"REMOTE_CHECKER_DO","class_name":"RemoteChecker"},'$CURRENT_BINDINGS']}'

          # By the ways enable logs
          curl --request PATCH \
               --url https://api.cloudflare.com/client/v4/accounts/$CLOUDFLARE_ACCOUNT_ID/workers/scripts/uptimeflare_worker/script-settings \
               --header 'Authorization: Bearer '$CLOUDFLARE_API_TOKEN \
               --header 'Content-Type: application/json' \
               -d '{"observability":{"enabled":true}}'
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ steps.fetch_account_id.outputs.account_id }}

      # Currently Terraform Cloudflare provider doesn't support direct upload, use wrangler to upload instead.
      - name: Upload pages
        run: |
          npx wrangler pages deploy .vercel/output/static --project-name uptimeflare
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ steps.fetch_account_id.outputs.account_id }}
