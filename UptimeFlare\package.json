{"name": "uptimeflare", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "preview": "npx @cloudflare/next-on-pages && wrangler pages dev .vercel/output/static --compatibility-flag nodejs_compat --kv UPTIMEFLARE_STATE", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@cloudflare/workers-types": "^4.20250410.0", "@mantine/core": "^7.1.3", "@mantine/ds": "^7.1.3", "@mantine/hooks": "^7.1.3", "@tabler/icons-react": "^2.39.0", "@types/moment-precise-range-plugin": "^0.2.2", "chart.js": "^4.4.0", "chartjs-adapter-moment": "^1.0.1", "moment": "^2.29.4", "moment-precise-range-plugin": "^1.3.0", "next": "^14.2.28", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.12", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "^14.2.28", "postcss": "^8.4.31", "postcss-preset-mantine": "^1.8.0", "postcss-simple-vars": "^7.0.1", "prettier": "3.0.3", "typescript": "^5", "wrangler": "^4.10.0"}}